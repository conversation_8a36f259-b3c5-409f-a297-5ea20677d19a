import React, { useEffect, useState } from 'react';
import './Parallax.css';

const Parallax = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section className="parallax">
      <div 
        className="parallax__background"
        style={{
          transform: `translateY(${scrollY * 0.5}px)`
        }}
      >
        <div className="parallax__shape parallax__shape--1"></div>
        <div className="parallax__shape parallax__shape--2"></div>
        <div className="parallax__shape parallax__shape--3"></div>
      </div>
      
      <div className="parallax__container">
        <div className="parallax__content">
          <div className="parallax__quote-mark">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
              <path d="M3 21C3 17.4 3 14.2 3 11.2C3 7.22 4.92 4 8 4C9.66 4 10.55 5.16 10.55 6.55C10.55 7.93 9.66 9.09 8 9.09C6.34 9.09 5.45 7.93 5.45 6.55C5.45 5.16 6.34 4 8 4C11.08 4 13 7.22 13 11.2C13 14.2 13 17.4 13 21H3Z" fill="currentColor"/>
              <path d="M14 21C14 17.4 14 14.2 14 11.2C14 7.22 15.92 4 19 4C20.66 4 21.55 5.16 21.55 6.55C21.55 7.93 20.66 9.09 19 9.09C17.34 9.09 16.45 7.93 16.45 6.55C16.45 5.16 17.34 4 19 4C22.08 4 24 7.22 24 11.2C24 14.2 24 17.4 24 21H14Z" fill="currentColor"/>
            </svg>
          </div>
          
          <blockquote className="parallax__text">
            At its core, having a virtual assistant means delivering specialized expertise, 
            staying adaptable to technological advancements, and facilitating the delegation 
            of routine tasks, thereby bolstering efficiency and supporting strategic growth.
          </blockquote>
          
          <div className="parallax__attribution">
            <div className="parallax__line"></div>
            <span className="parallax__author">iProvide VA Solutions</span>
          </div>
        </div>
      </div>
      
      <div className="parallax__overlay"></div>
    </section>
  );
};

export default Parallax;
