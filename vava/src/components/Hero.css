/* Modern Centered Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  overflow: hidden;
  padding: 120px 0 80px;
}

.hero__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 32px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero__content {
  display: flex;
  flex-direction: column;
  gap: 32px;
  text-align: left;
  animation: slideInLeft 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  opacity: 0;
  transform: translateX(-50px);
}

/* Modern Badge */
.hero__badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  border: 1px solid rgba(249, 91, 0, 0.2);
  border-radius: 24px;
  backdrop-filter: blur(8px);
  width: fit-content;
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 0.2s forwards;
  opacity: 0;
  transform: translateY(20px);
}

.hero__badge-text {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-orange);
  letter-spacing: -0.01em;
}

/* Modern Title */
.hero__title {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 0;
}

.hero__title-line {
  font-size: 56px;
  font-weight: 700;
  line-height: 1.1;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 0.4s forwards;
  opacity: 0;
  transform: translateY(30px);
}

.hero__title-subtitle {
  font-size: 24px;
  font-weight: 500;
  color: var(--text-secondary);
  letter-spacing: -0.01em;
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 0.6s forwards;
  opacity: 0;
  transform: translateY(20px);
}

/* Modern Description */
.hero__description {
  font-size: 18px;
  line-height: 1.6;
  color: var(--text-secondary);
  max-width: 520px;
  margin: 0;
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 0.8s forwards;
  opacity: 0;
  transform: translateY(20px);
}

/* Modern Actions */
.hero__actions {
  display: flex;
  gap: 16px;
  align-items: center;
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 1s forwards;
  opacity: 0;
  transform: translateY(20px);
}

.hero__cta-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
  color: white;
  border: none;
  border-radius: 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  font-family: 'DM Sans', sans-serif;
  box-shadow:
    0 8px 32px rgba(249, 91, 0, 0.25),
    0 4px 16px rgba(3, 124, 193, 0.15);
  position: relative;
  overflow: hidden;
}

.hero__cta-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.hero__cta-primary:hover::before {
  left: 100%;
}

.hero__cta-primary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 48px rgba(249, 91, 0, 0.35),
    0 6px 24px rgba(3, 124, 193, 0.25);
}

.hero__cta-icon {
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.hero__cta-primary:hover .hero__cta-icon {
  transform: translateX(4px);
}

.hero__cta-secondary {
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.8);
  color: var(--text-primary);
  border: 1.5px solid rgba(0, 0, 0, 0.08);
  border-radius: 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  font-family: 'DM Sans', sans-serif;
  backdrop-filter: blur(8px);
}

.hero__cta-secondary:hover {
  background: rgba(255, 255, 255, 1);
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(3, 124, 193, 0.15);
}

/* Modern Stats */
.hero__stats {
  display: flex;
  gap: 48px;
  padding: 24px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 1.2s forwards;
  opacity: 0;
  transform: translateY(20px);
}

.hero__stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.hero__stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-orange);
  line-height: 1;
  animation: countUp 2s ease-out 1.5s forwards;
}

.hero__stat-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Modern Image Section */
.hero__image {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: slideInRight 1s cubic-bezier(0.16, 1, 0.3, 1) 0.5s forwards;
  opacity: 0;
  transform: translateX(50px);
}

.hero__image-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  border-radius: 24px;
  overflow: hidden;
  box-shadow:
    0 20px 80px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.hero__image-container:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 120px rgba(0, 0, 0, 0.2),
    0 12px 48px rgba(0, 0, 0, 0.15);
}

.hero__image-main {
  width: 100%;
  height: 500px;
  object-fit: cover;
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  display: block;
}

.hero__image-container:hover .hero__image-main {
  transform: scale(1.05);
}

.hero__image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.hero__image-container:hover .hero__image-overlay {
  opacity: 1;
}

/* Modern Background */
.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}

.hero__bg-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  animation: float 6s ease-in-out infinite;
}

.hero__bg-shape--1 {
  width: 300px;
  height: 300px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.hero__bg-shape--2 {
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: 5%;
  animation-delay: 2s;
}

.hero__bg-shape--3 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 30%;
  animation-delay: 4s;
}

/* Keyframe Animations */
@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes countUp {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modern Responsive Design */
@media (max-width: 1024px) {
  .hero__container {
    gap: 60px;
    padding: 0 24px;
  }

  .hero__title-line {
    font-size: 48px;
  }

  .hero__title-subtitle {
    font-size: 20px;
  }

  .hero__description {
    font-size: 16px;
  }

  .hero__image-main {
    height: 400px;
  }

  .hero__bg-shape--1 {
    width: 200px;
    height: 200px;
  }

  .hero__bg-shape--2 {
    width: 150px;
    height: 150px;
  }

  .hero__bg-shape--3 {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 100px 0 60px;
  }

  .hero__container {
    grid-template-columns: 1fr;
    gap: 48px;
    text-align: center;
  }

  .hero__content {
    animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    transform: translateY(30px);
  }

  .hero__image {
    animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 0.3s forwards;
    transform: translateY(30px);
  }

  .hero__title-line {
    font-size: 40px;
  }

  .hero__title-subtitle {
    font-size: 18px;
  }

  .hero__description {
    max-width: 100%;
  }

  .hero__actions {
    justify-content: center;
  }

  .hero__stats {
    justify-content: center;
    gap: 32px;
  }

  .hero__image-main {
    height: 350px;
  }

  .hero__bg-shape {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 80px 0 40px;
  }

  .hero__container {
    padding: 0 16px;
    gap: 32px;
  }

  .hero__title-line {
    font-size: 32px;
  }

  .hero__title-subtitle {
    font-size: 16px;
  }

  .hero__description {
    font-size: 14px;
  }

  .hero__actions {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .hero__cta-primary,
  .hero__cta-secondary {
    width: 100%;
    justify-content: center;
  }

  .hero__stats {
    gap: 24px;
    padding: 16px 0;
  }

  .hero__stat-number {
    font-size: 24px;
  }

  .hero__stat-label {
    font-size: 12px;
  }

  .hero__image-main {
    height: 250px;
  }

  .hero__image-container {
    border-radius: 16px;
  }
}
