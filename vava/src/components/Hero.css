/* Modern Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.hero__container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 32px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero__content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Badge */
.hero__badge {
  display: inline-flex;
  align-items: center;
  width: fit-content;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  border: 1px solid rgba(249, 91, 0, 0.2);
  border-radius: 24px;
  backdrop-filter: blur(8px);
}

.hero__badge-text {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-orange);
  letter-spacing: -0.01em;
}

/* Title */
.hero__title {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hero__title-main {
  font-size: 56px;
  font-weight: 700;
  line-height: 1.1;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero__title-sub {
  font-size: 24px;
  font-weight: 500;
  color: var(--text-secondary);
  letter-spacing: -0.01em;
}

/* Description */
.hero__description {
  font-size: 18px;
  line-height: 1.6;
  color: var(--text-secondary);
  max-width: 540px;
  letter-spacing: -0.01em;
}

/* Stats */
.hero__stats {
  display: flex;
  gap: 48px;
  padding: 24px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.hero__stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.hero__stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-orange);
  line-height: 1;
}

.hero__stat-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Actions */
.hero__actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.hero__cta-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
  color: white;
  border: none;
  border-radius: 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 
    0 8px 32px rgba(249, 91, 0, 0.25),
    0 4px 16px rgba(3, 124, 193, 0.15);
  position: relative;
  overflow: hidden;
}

.hero__cta-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.hero__cta-primary:hover::before {
  left: 100%;
}

.hero__cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 48px rgba(249, 91, 0, 0.35),
    0 6px 24px rgba(3, 124, 193, 0.25);
}

.hero__cta-icon {
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.hero__cta-primary:hover .hero__cta-icon {
  transform: translateX(4px);
}

.hero__cta-secondary {
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.8);
  color: var(--text-primary);
  border: 1.5px solid rgba(0, 0, 0, 0.08);
  border-radius: 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  backdrop-filter: blur(8px);
}

.hero__cta-secondary:hover {
  background: rgba(255, 255, 255, 1);
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(3, 124, 193, 0.15);
}

/* Trust Section */
.hero__trust {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.hero__trust-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.hero__trust-logos {
  display: flex;
  gap: 24px;
  align-items: center;
}

.hero__trust-logo {
  padding: 8px 16px;
  background: rgba(245, 245, 247, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  backdrop-filter: blur(8px);
}

/* Visual Section */
.hero__visual {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
}

.hero__visual-card {
  padding: 32px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  backdrop-filter: blur(16px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
}

.hero__visual-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 16px 64px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08);
}

.hero__visual-card--1 {
  transform: translateX(-20px);
}

.hero__visual-card--2 {
  transform: translateX(20px);
}

.hero__visual-card--3 {
  transform: translateX(-10px);
}

.hero__visual-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 16px;
}

.hero__visual-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  letter-spacing: -0.01em;
}

.hero__visual-card p {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Background */
.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero__gradient {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.05), rgba(3, 124, 193, 0.05));
  opacity: 0.8;
}

.hero__pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(249, 91, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(3, 124, 193, 0.1) 0%, transparent 50%);
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero__container {
    gap: 60px;
    padding: 0 24px;
  }

  .hero__title-main {
    font-size: 48px;
  }

  .hero__title-sub {
    font-size: 20px;
  }

  .hero__description {
    font-size: 16px;
  }

  .hero__stats {
    gap: 32px;
  }

  .hero__visual-card {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: auto;
    padding: 120px 0 80px;
  }

  .hero__container {
    grid-template-columns: 1fr;
    gap: 48px;
    text-align: center;
  }

  .hero__content {
    order: 1;
  }

  .hero__visual {
    order: 2;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .hero__title-main {
    font-size: 40px;
  }

  .hero__title-sub {
    font-size: 18px;
  }

  .hero__description {
    font-size: 16px;
    max-width: 100%;
  }

  .hero__stats {
    justify-content: center;
    gap: 24px;
  }

  .hero__actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .hero__trust {
    align-items: center;
  }

  .hero__trust-logos {
    justify-content: center;
    flex-wrap: wrap;
  }

  .hero__visual-card {
    flex: 1;
    min-width: 200px;
    max-width: 280px;
    transform: none !important;
  }

  .hero__visual-card--2 {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 100px 0 60px;
  }

  .hero__container {
    padding: 0 16px;
    gap: 32px;
  }

  .hero__title-main {
    font-size: 32px;
  }

  .hero__title-sub {
    font-size: 16px;
  }

  .hero__description {
    font-size: 14px;
  }

  .hero__stats {
    gap: 16px;
    padding: 16px 0;
  }

  .hero__stat-number {
    font-size: 24px;
  }

  .hero__stat-label {
    font-size: 12px;
  }

  .hero__actions {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .hero__cta-primary,
  .hero__cta-secondary {
    width: 100%;
    justify-content: center;
  }

  .hero__trust-logos {
    gap: 12px;
  }

  .hero__trust-logo {
    font-size: 10px;
    padding: 6px 12px;
  }

  .hero__visual {
    flex-direction: column;
    align-items: center;
  }

  .hero__visual-card {
    width: 100%;
    max-width: 100%;
    padding: 20px;
  }

  .hero__visual-card--3 {
    display: none;
  }
}
