/* Minimalistic Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: #ffffff;
  padding: 120px 0 80px;
}

.hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 32px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.hero__content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Minimalistic Title */
.hero__title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  margin: 0;
}

/* Minimalistic Description */
.hero__description {
  font-size: 18px;
  line-height: 1.6;
  color: var(--text-secondary);
  max-width: 480px;
  margin: 0;
}

/* Minimalistic Actions */
.hero__actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.hero__cta-primary {
  padding: 14px 28px;
  background: var(--primary-orange);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'DM Sans', sans-serif;
}

.hero__cta-primary:hover {
  background: #e54f00;
  transform: translateY(-1px);
}

.hero__cta-secondary {
  padding: 14px 28px;
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--text-primary);
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'DM Sans', sans-serif;
}

.hero__cta-secondary:hover {
  background: var(--text-primary);
  color: white;
  transform: translateY(-1px);
}

/* Minimalistic Image Section */
.hero__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero__image-main {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Minimalistic Responsive Design */
@media (max-width: 1024px) {
  .hero__container {
    gap: 60px;
    padding: 0 24px;
  }

  .hero__title {
    font-size: 42px;
  }

  .hero__description {
    font-size: 16px;
  }

  .hero__image-main {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 100px 0 60px;
  }

  .hero__container {
    grid-template-columns: 1fr;
    gap: 48px;
    text-align: center;
  }

  .hero__title {
    font-size: 36px;
  }

  .hero__description {
    max-width: 100%;
  }

  .hero__actions {
    justify-content: center;
  }

  .hero__image-main {
    height: 350px;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 80px 0 40px;
  }

  .hero__container {
    padding: 0 16px;
    gap: 32px;
  }

  .hero__title {
    font-size: 28px;
  }

  .hero__description {
    font-size: 14px;
  }

  .hero__actions {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .hero__cta-primary,
  .hero__cta-secondary {
    width: 100%;
    justify-content: center;
  }

  .hero__image-main {
    height: 250px;
  }
}
