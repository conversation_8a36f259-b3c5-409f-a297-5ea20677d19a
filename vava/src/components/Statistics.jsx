import React, { useState, useEffect, useRef } from 'react';
import './Statistics.css';

const Statistics = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [counts, setCounts] = useState({
    clients: 0,
    traffic: 0,
    revenue: 0
  });
  const sectionRef = useRef(null);

  const stats = [
    {
      id: 'clients',
      number: '90+',
      label: 'Satisfied Clients',
      target: 90,
      suffix: '+'
    },
    {
      id: 'traffic',
      number: '120%',
      label: 'Increased Traffic',
      target: 120,
      suffix: '%'
    },
    {
      id: 'revenue',
      number: '72%',
      label: 'Increased Revenue',
      target: 72,
      suffix: '%'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          animateCounters();
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  const animateCounters = () => {
    stats.forEach((stat) => {
      let start = 0;
      const end = stat.target;
      const duration = 2000;
      const increment = end / (duration / 16);

      const timer = setInterval(() => {
        start += increment;
        if (start >= end) {
          start = end;
          clearInterval(timer);
        }
        
        setCounts(prev => ({
          ...prev,
          [stat.id]: Math.floor(start)
        }));
      }, 16);
    });
  };

  return (
    <section className="statistics" ref={sectionRef}>
      <div className="statistics__container">
        <div className="statistics__content">
          <div className="statistics__header">
            <div className="statistics__badge">
              <span className="statistics__badge-text">🇵🇭 VAs of the Philippines</span>
            </div>
            
            <h2 className="statistics__title">
              World-Class Virtual Assistants from the Philippines
            </h2>
            
            <div className="statistics__description">
              <p>
                Our virtual assistants hail from the Philippines, a country renowned for its pool of 
                highly skilled and adaptable professionals.
              </p>
              
              <p>
                The Philippines has become a global hub for outsourcing due to its strategic location, 
                cultural compatibility, and the government's support for the business process 
                outsourcing (BPO) industry.
              </p>
              
              <p>
                By choosing virtual assistants from the Philippines, we not only benefit from their 
                technical proficiency but also from their commitment to delivering top-notch service, 
                contributing to the success and efficiency of businesses around the globe.
              </p>
            </div>
          </div>

          <div className="statistics__stats">
            <div className="statistics__stats-grid">
              {stats.map((stat) => (
                <div key={stat.id} className="statistics__stat-card">
                  <div className="statistics__stat-number">
                    {stat.id === 'clients' && `${counts.clients}${stat.suffix}`}
                    {stat.id === 'traffic' && `${counts.traffic}${stat.suffix}`}
                    {stat.id === 'revenue' && `${counts.revenue}${stat.suffix}`}
                  </div>
                  <div className="statistics__stat-label">{stat.label}</div>
                  <div className="statistics__stat-bar">
                    <div 
                      className="statistics__stat-fill"
                      style={{
                        width: isVisible ? '100%' : '0%',
                        transitionDelay: `${stats.indexOf(stat) * 0.2}s`
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="statistics__visual">
          <div className="statistics__map-container">
            <div className="statistics__map-bg">
              <svg viewBox="0 0 400 300" className="statistics__map">
                {/* Simplified Philippines outline */}
                <path
                  d="M200 50 L220 60 L240 80 L250 100 L260 120 L270 140 L280 160 L275 180 L270 200 L260 220 L250 240 L230 250 L210 245 L190 240 L170 230 L150 220 L140 200 L135 180 L140 160 L150 140 L160 120 L170 100 L180 80 L190 70 L200 50 Z"
                  className="statistics__map-path"
                />
                <circle cx="200" cy="150" r="8" className="statistics__map-pin" />
              </svg>
            </div>
            
            <div className="statistics__floating-elements">
              <div className="statistics__floating-item statistics__floating-item--1">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2"/>
                  <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
                  <path d="M23 21V19C23 18.1645 22.7155 17.3541 22.2094 16.6977C21.7033 16.0414 20.9999 15.5735 20.2 15.3613" stroke="currentColor" strokeWidth="2"/>
                  <path d="M16 3.13C16.8003 3.3422 17.5037 3.81014 18.0098 4.46645C18.5159 5.12277 18.8004 5.93317 18.8004 6.76875C18.8004 7.60433 18.5159 8.41473 18.0098 9.07105C17.5037 9.72736 16.8003 10.1953 16 10.4075" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              
              <div className="statistics__floating-item statistics__floating-item--2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              
              <div className="statistics__floating-item statistics__floating-item--3">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Statistics;
