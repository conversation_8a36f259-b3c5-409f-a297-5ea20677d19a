/* Parallax Section */
.parallax {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
}

.parallax__background {
  position: absolute;
  top: -20%;
  left: 0;
  right: 0;
  bottom: -20%;
  z-index: 1;
  will-change: transform;
}

.parallax__shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  backdrop-filter: blur(40px);
}

.parallax__shape--1 {
  width: 400px;
  height: 400px;
  top: 10%;
  left: -10%;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.15), rgba(3, 124, 193, 0.05));
}

.parallax__shape--2 {
  width: 300px;
  height: 300px;
  top: 60%;
  right: -5%;
  background: linear-gradient(135deg, rgba(3, 124, 193, 0.15), rgba(249, 91, 0, 0.05));
}

.parallax__shape--3 {
  width: 200px;
  height: 200px;
  top: 30%;
  right: 20%;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.08), rgba(3, 124, 193, 0.12));
}

.parallax__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 32px;
  position: relative;
  z-index: 3;
  text-align: center;
}

.parallax__content {
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.parallax__quote-mark {
  color: var(--primary-orange);
  opacity: 0.8;
  margin-bottom: 20px;
}

.parallax__text {
  font-size: 32px;
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: -0.01em;
  margin: 0;
  color: rgba(255, 255, 255, 0.95);
  font-style: italic;
  position: relative;
}

.parallax__attribution {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
}

.parallax__line {
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-orange), var(--primary-blue));
  border-radius: 1px;
}

.parallax__author {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.parallax__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 20%, rgba(249, 91, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(3, 124, 193, 0.1) 0%, transparent 50%);
  z-index: 2;
  pointer-events: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .parallax__container {
    padding: 0 24px;
  }

  .parallax__text {
    font-size: 28px;
  }

  .parallax__shape--1 {
    width: 300px;
    height: 300px;
  }

  .parallax__shape--2 {
    width: 250px;
    height: 250px;
  }

  .parallax__shape--3 {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 768px) {
  .parallax {
    min-height: 80vh;
    padding: 80px 0;
  }

  .parallax__container {
    padding: 0 20px;
  }

  .parallax__content {
    gap: 30px;
  }

  .parallax__text {
    font-size: 24px;
    line-height: 1.5;
  }

  .parallax__quote-mark svg {
    width: 36px;
    height: 36px;
  }

  .parallax__attribution {
    gap: 15px;
  }

  .parallax__line {
    width: 40px;
  }

  .parallax__author {
    font-size: 14px;
  }

  .parallax__shape--1 {
    width: 200px;
    height: 200px;
    left: -20%;
  }

  .parallax__shape--2 {
    width: 180px;
    height: 180px;
    right: -15%;
  }

  .parallax__shape--3 {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .parallax {
    min-height: 70vh;
    padding: 60px 0;
  }

  .parallax__container {
    padding: 0 16px;
  }

  .parallax__content {
    gap: 24px;
  }

  .parallax__text {
    font-size: 20px;
    line-height: 1.6;
  }

  .parallax__quote-mark svg {
    width: 32px;
    height: 32px;
  }

  .parallax__attribution {
    flex-direction: column;
    gap: 12px;
  }

  .parallax__line {
    width: 30px;
  }

  .parallax__author {
    font-size: 12px;
  }

  .parallax__shape--1,
  .parallax__shape--2 {
    display: none;
  }

  .parallax__shape--3 {
    width: 80px;
    height: 80px;
    top: 20%;
    right: 10%;
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .parallax__background {
    transform: none !important;
  }
}
