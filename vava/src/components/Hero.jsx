import React from 'react';
import './Hero.css';

const Hero = () => {
  const handleContactClick = () => {
    document.querySelector('#contact')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  const handleServicesClick = () => {
    document.querySelector('#va-services')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  return (
    <section className="hero">
      <div className="hero__container">
        <div className="hero__content">
          <h1 className="hero__title">iProvide VA Solutions</h1>

          <p className="hero__description">
            Transform your business with highly skilled virtual assistants.
            Cost-effective solutions that streamline operations and boost productivity.
          </p>

          <div className="hero__actions">
            <button
              className="hero__cta-primary"
              onClick={handleContactClick}
            >
              Get Started
            </button>

            <button
              className="hero__cta-secondary"
              onClick={handleServicesClick}
            >
              Learn More
            </button>
          </div>
        </div>

        <div className="hero__image">
          <img
            src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Professional virtual assistant working remotely"
            className="hero__image-main"
          />
        </div>
      </div>
    </section>
  );
};

export default Hero;
