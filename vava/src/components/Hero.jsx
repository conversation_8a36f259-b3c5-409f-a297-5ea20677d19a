import React from 'react';
import './Hero.css';

const Hero = () => {
  const handleContactClick = () => {
    document.querySelector('#contact')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  const handleServicesClick = () => {
    document.querySelector('#va-services')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  return (
    <section className="hero">
      <div className="hero__container">
        <div className="hero__content">
          <div className="hero__badge">
            <span className="hero__badge-text">✨ Trusted by 500+ Businesses</span>
          </div>

          <h1 className="hero__title">
            <span className="hero__title-line">iProvide VA Solutions</span>
            <span className="hero__title-subtitle">Your Success, Our Expertise</span>
          </h1>

          <p className="hero__description">
            Transform your business with highly skilled virtual assistants.
            Cost-effective solutions that streamline operations and boost productivity
            for US business owners.
          </p>

          <div className="hero__actions">
            <button
              className="hero__cta-primary"
              onClick={handleContactClick}
            >
              <span>Get Started Today</span>
              <svg className="hero__cta-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M6 12L10 8L6 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            <button
              className="hero__cta-secondary"
              onClick={handleServicesClick}
            >
              Learn More
            </button>
          </div>

          <div className="hero__stats">
            <div className="hero__stat">
              <span className="hero__stat-number">500+</span>
              <span className="hero__stat-label">Happy Clients</span>
            </div>
            <div className="hero__stat">
              <span className="hero__stat-number">98%</span>
              <span className="hero__stat-label">Success Rate</span>
            </div>
            <div className="hero__stat">
              <span className="hero__stat-number">24/7</span>
              <span className="hero__stat-label">Support</span>
            </div>
          </div>
        </div>

        <div className="hero__image">
          <div className="hero__image-container">
            <img
              src="/pexels-yankrukov-8866806.jpg"
              alt="Professional virtual assistant working remotely"
              className="hero__image-main"
            />
            <div className="hero__image-overlay"></div>
          </div>
        </div>
      </div>

      <div className="hero__background">
        <div className="hero__bg-shape hero__bg-shape--1"></div>
        <div className="hero__bg-shape hero__bg-shape--2"></div>
        <div className="hero__bg-shape hero__bg-shape--3"></div>
      </div>
    </section>
  );
};

export default Hero;
