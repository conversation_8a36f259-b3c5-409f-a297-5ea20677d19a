import React from 'react';
import './Hero.css';

const Hero = () => {
  const handleContactClick = () => {
    document.querySelector('#contact')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  const handleServicesClick = () => {
    document.querySelector('#va-services')?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  return (
    <section className="hero">
      <div className="hero__container">
        <div className="hero__content">
          <div className="hero__badge">
            <span className="hero__badge-text">Trusted by 500+ US Businesses</span>
          </div>
          
          <h1 className="hero__title">
            <span className="hero__title-main">iProvide VA Solutions</span>
            <span className="hero__title-sub">Your Success, Our Expertise</span>
          </h1>
          
          <p className="hero__description">
            Transform your business with our highly skilled virtual assistants. We provide 
            cost-effective solutions that streamline operations, boost productivity, and 
            deliver specialized expertise for your specific projects and tasks.
          </p>
          
          <div className="hero__stats">
            <div className="hero__stat">
              <span className="hero__stat-number">500+</span>
              <span className="hero__stat-label">Happy Clients</span>
            </div>
            <div className="hero__stat">
              <span className="hero__stat-number">98%</span>
              <span className="hero__stat-label">Success Rate</span>
            </div>
            <div className="hero__stat">
              <span className="hero__stat-number">24/7</span>
              <span className="hero__stat-label">Support</span>
            </div>
          </div>
          
          <div className="hero__actions">
            <button 
              className="hero__cta-primary"
              onClick={handleContactClick}
            >
              Get Started Today
              <svg className="hero__cta-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M6 12L10 8L6 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            
            <button 
              className="hero__cta-secondary"
              onClick={handleServicesClick}
            >
              Explore Services
            </button>
          </div>
          
          <div className="hero__trust">
            <span className="hero__trust-text">Trusted by leading companies</span>
            <div className="hero__trust-logos">
              <div className="hero__trust-logo">Microsoft Partner</div>
              <div className="hero__trust-logo">Google Certified</div>
              <div className="hero__trust-logo">AWS Partner</div>
            </div>
          </div>
        </div>
        
        <div className="hero__visual">
          <div className="hero__visual-card hero__visual-card--1">
            <div className="hero__visual-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h3>Expert VAs</h3>
            <p>Skilled professionals ready to elevate your business</p>
          </div>
          
          <div className="hero__visual-card hero__visual-card--2">
            <div className="hero__visual-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h3>Fast Results</h3>
            <p>See immediate impact on your productivity</p>
          </div>
          
          <div className="hero__visual-card hero__visual-card--3">
            <div className="hero__visual-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 1V23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M17 5H9.5A3.5 3.5 0 0 0 6 8.5V8.5A3.5 3.5 0 0 0 9.5 12H14.5A3.5 3.5 0 0 1 18 15.5V15.5A3.5 3.5 0 0 1 14.5 19H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h3>Cost Effective</h3>
            <p>Save up to 70% on operational costs</p>
          </div>
        </div>
      </div>
      
      <div className="hero__background">
        <div className="hero__gradient"></div>
        <div className="hero__pattern"></div>
      </div>
    </section>
  );
};

export default Hero;
