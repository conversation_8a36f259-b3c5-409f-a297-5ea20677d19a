/* Apple-Style Services Section */
.services {
  padding: 120px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
}

.services__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 32px;
}

/* Header Section */
.services__header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  margin-bottom: 80px;
}

.services__header-content {
  text-align: left;
}

.services__header-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.services__badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  border: 1px solid rgba(249, 91, 0, 0.2);
  border-radius: 24px;
  backdrop-filter: blur(8px);
  margin-bottom: 24px;
}

.services__badge-text {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-orange);
  letter-spacing: -0.01em;
  text-transform: uppercase;
}

.services__title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  margin: 0 0 24px 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.services__description {
  font-size: 18px;
  line-height: 1.6;
  color: var(--text-secondary);
  max-width: 500px;
  margin: 0;
}

/* Visual Element */
.services__visual-container {
  position: relative;
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.services__visual-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 40px;
}

.services__visual-item {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.services__visual-item svg {
  width: 24px;
  height: 24px;
  color: var(--primary-blue);
}

/* Grid Layout */
.services__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-top: 80px;
}

/* Apple-Style Cards */
.services__card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 32px;
  backdrop-filter: blur(16px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.services__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.02), rgba(3, 124, 193, 0.02));
  opacity: 0;
  border-radius: 20px;
}

.services__card:hover::before {
  opacity: 1;
}

.services__card:hover {
  box-shadow:
    0 12px 48px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.06);
  border-color: rgba(249, 91, 0, 0.1);
}

/* Card Icon */
.services__card-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 24px;
}

/* Card Content */
.services__card-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
  letter-spacing: -0.01em;
}

.services__card-description {
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin: 0 0 24px 0;
}

/* Card Arrow */
.services__card-arrow {
  position: absolute;
  bottom: 24px;
  right: 24px;
  width: 32px;
  height: 32px;
  background: rgba(249, 91, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-orange);
  opacity: 0;
}

.services__card:hover .services__card-arrow {
  opacity: 1;
  background: var(--primary-orange);
  color: white;
}



/* Responsive Design */
@media (max-width: 1024px) {
  .services {
    padding: 80px 0;
  }

  .services__container {
    padding: 0 24px;
  }

  .services__title {
    font-size: 40px;
  }

  .services__grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .services__card {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .services {
    padding: 60px 0;
  }

  .services__header {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 60px;
    text-align: center;
  }

  .services__header-content {
    text-align: center;
  }

  .services__visual-container {
    width: 300px;
    height: 300px;
    margin: 0 auto;
  }

  .services__visual-grid {
    padding: 30px;
    gap: 12px;
  }

  .services__visual-item {
    width: 50px;
    height: 50px;
  }

  .services__title {
    font-size: 32px;
  }

  .services__description {
    font-size: 16px;
    max-width: 100%;
  }

  .services__grid {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-top: 60px;
  }

  .services__card {
    padding: 20px;
  }

  .services__card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
  }
}

@media (max-width: 480px) {
  .services__container {
    padding: 0 16px;
  }

  .services__visual-container {
    width: 250px;
    height: 250px;
  }

  .services__visual-grid {
    padding: 20px;
    gap: 8px;
  }

  .services__visual-item {
    width: 40px;
    height: 40px;
  }

  .services__visual-item svg {
    width: 20px;
    height: 20px;
  }

  .services__title {
    font-size: 28px;
  }

  .services__description {
    font-size: 14px;
  }

  .services__card {
    padding: 16px;
  }

  .services__card-title {
    font-size: 18px;
  }

  .services__card-description {
    font-size: 13px;
  }
}
