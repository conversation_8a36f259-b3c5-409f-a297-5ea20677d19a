/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding: 0;
}

.header--scrolled {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header__container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 72px;
  gap: 24px;
}

/* Logo Styles */
.header__logo {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  transition: transform 0.2s ease;
}

.header__logo:hover {
  transform: scale(1.02);
}

.header__logo-image {
  height: 44px;
  width: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.header__logo:hover .header__logo-image {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
}

/* Desktop Navigation */
.header__nav {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.header__nav-list {
  display: flex;
  align-items: center;
  gap: 8px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.header__nav-item {
  position: relative;
}

.header__nav-link {
  display: block;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 20px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.header__nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 20px;
}

.header__nav-link:hover::before {
  opacity: 0.1;
}

.header__nav-link:hover {
  color: var(--primary-blue);
  transform: translateY(-1px);
}

.header__nav-link:active {
  transform: translateY(0);
}

/* CTA Section */
.header__cta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header__cta-button {
  padding: 10px 20px;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  min-width: 100px;
}

.header__cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.header__cta-button:hover::before {
  left: 100%;
}

.header__cta-button--secondary {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border);
}

.header__cta-button--secondary:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(3, 124, 193, 0.2);
}

.header__cta-button--primary {
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
  color: white;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(249, 91, 0, 0.3);
}

.header__cta-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(249, 91, 0, 0.4);
}

.header__cta-button:active {
  transform: translateY(0);
}

/* Mobile Menu Button */
.header__mobile-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  gap: 4px;
  transition: transform 0.2s ease;
}

.header__mobile-toggle:hover {
  transform: scale(1.1);
}

.header__mobile-toggle-line {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  border-radius: 1px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
}

.header__mobile-toggle--active .header__mobile-toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(3px, 3px);
}

.header__mobile-toggle--active .header__mobile-toggle-line:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.header__mobile-toggle--active .header__mobile-toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(3px, -3px);
}

/* Mobile Navigation */
.header__mobile-nav {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header__mobile-nav--open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.header__mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header__mobile-nav-link {
  display: block;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.2s ease;
  background: rgba(245, 245, 247, 0.5);
}

.header__mobile-nav-link:hover {
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  color: var(--primary-blue);
  transform: translateX(8px);
}

/* Mobile Menu Overlay */
.header__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: -1;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header__container {
    padding: 0 16px;
    height: 64px;
  }

  .header__nav {
    display: none;
  }

  .header__mobile-toggle {
    display: flex;
  }

  .header__logo-image {
    height: 36px;
  }

  .header__cta {
    gap: 8px;
  }

  .header__cta-button {
    padding: 8px 16px;
    font-size: 13px;
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .header__container {
    padding: 0 12px;
    gap: 12px;
  }

  .header__logo-image {
    height: 32px;
  }

  .header__cta {
    gap: 6px;
  }

  .header__cta-button {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 70px;
  }

  .header__cta-button--secondary {
    display: none; /* Hide secondary button on very small screens */
  }
}
