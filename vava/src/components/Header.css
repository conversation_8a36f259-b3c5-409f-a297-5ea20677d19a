/* Modern Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(24px) saturate(180%);
  -webkit-backdrop-filter: blur(24px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  padding: 0;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.1) inset;
}

.header--scrolled {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow:
    0 1px 0 rgba(255, 255, 255, 0.1) inset,
    0 8px 32px rgba(0, 0, 0, 0.12);
}

.header__container {
  max-width: 1440px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  height: 80px;
  gap: 32px;
}

/* Modern Logo Styles */
.header__logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  cursor: pointer;
}

.header__logo:hover {
  transform: translateY(-1px);
}

.header__logo-image {
  height: 48px;
  width: auto;
  border-radius: 12px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  filter: brightness(1.02) contrast(1.05);
}

.header__logo:hover .header__logo-image {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
  transform: scale(1.03);
}

/* Modern Desktop Navigation */
.header__nav {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.header__nav-list {
  display: flex;
  align-items: center;
  gap: 4px;
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(245, 245, 247, 0.6);
  border-radius: 24px;
  padding: 6px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header__nav-item {
  position: relative;
}

.header__nav-link {
  display: block;
  padding: 10px 18px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 18px;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.header__nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  border-radius: 18px;
  transform: scale(0.8);
}

.header__nav-link:hover::before {
  opacity: 0.12;
  transform: scale(1);
}

.header__nav-link:hover {
  color: var(--primary-blue);
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.header__nav-link:active {
  transform: translateY(0);
}

/* Modern CTA Section */
.header__cta {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header__cta-button {
  padding: 12px 24px;
  border-radius: 28px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  min-width: 110px;
  font-family: 'DM Sans', sans-serif;
  letter-spacing: -0.01em;
}

.header__cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.header__cta-button:hover::before {
  left: 100%;
}

.header__cta-button--secondary {
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-primary);
  border: 1.5px solid rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    0 1px 2px rgba(0, 0, 0, 0.02);
}

.header__cta-button--secondary:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(3, 124, 193, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 1);
}

.header__cta-button--primary {
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-blue) 100%);
  color: white;
  border: 1.5px solid transparent;
  box-shadow:
    0 4px 16px rgba(249, 91, 0, 0.25),
    0 2px 8px rgba(3, 124, 193, 0.15);
}

.header__cta-button--primary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 32px rgba(249, 91, 0, 0.35),
    0 4px 16px rgba(3, 124, 193, 0.25);
  background: linear-gradient(135deg, #ff6b1a 0%, #0487d1 100%);
}

.header__cta-button:active {
  transform: translateY(0);
}

/* Modern Mobile Menu Button */
.header__mobile-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: rgba(245, 245, 247, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
  padding: 0;
  gap: 4px;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  backdrop-filter: blur(8px);
}

.header__mobile-toggle:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.header__mobile-toggle-line {
  width: 18px;
  height: 2px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center;
}

.header__mobile-toggle--active {
  background: rgba(255, 255, 255, 0.95);
}

.header__mobile-toggle--active .header__mobile-toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(3px, 3px);
}

.header__mobile-toggle--active .header__mobile-toggle-line:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.header__mobile-toggle--active .header__mobile-toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(3px, -3px);
}

/* Modern Mobile Navigation */
.header__mobile-nav {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px) saturate(180%);
  -webkit-backdrop-filter: blur(24px) saturate(180%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08);
}

.header__mobile-nav--open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.header__mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.header__mobile-nav-link {
  display: block;
  padding: 18px 24px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  background: rgba(245, 245, 247, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
}

.header__mobile-nav-link:hover {
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.12), rgba(3, 124, 193, 0.12));
  color: var(--primary-blue);
  transform: translateX(8px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: rgba(3, 124, 193, 0.2);
}

/* Mobile Menu Overlay */
.header__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: -1;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modern Responsive Design */
@media (max-width: 768px) {
  .header__container {
    padding: 0 20px;
    height: 72px;
  }

  .header__nav {
    display: none;
  }

  .header__mobile-toggle {
    display: flex;
  }

  .header__logo-image {
    height: 40px;
  }

  .header__cta {
    gap: 12px;
  }

  .header__cta-button {
    padding: 10px 18px;
    font-size: 13px;
    min-width: 90px;
    border-radius: 24px;
  }
}

@media (max-width: 480px) {
  .header__container {
    padding: 0 16px;
    gap: 16px;
    height: 68px;
  }

  .header__logo-image {
    height: 36px;
  }

  .header__cta {
    gap: 8px;
  }

  .header__cta-button {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 75px;
    border-radius: 20px;
  }

  .header__cta-button--secondary {
    display: none; /* Hide secondary button on very small screens */
  }

  .header__mobile-toggle {
    width: 36px;
    height: 36px;
  }

  .header__mobile-toggle-line {
    width: 16px;
  }
}
