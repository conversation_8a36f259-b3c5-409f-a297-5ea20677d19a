import React, { useState, useEffect } from 'react';
import './Header.css';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    const handleClickOutside = (event) => {
      if (!event.target.closest('.header__nav-item--dropdown')) {
        setActiveDropdown(null);
      }
    };

    window.addEventListener('scroll', handleScroll);
    document.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const navigationItems = [
    { name: 'Why Choose Us', href: '#why-choose-us' },
    {
      name: 'Services',
      isDropdown: true,
      items: [
        { name: 'VA Solutions', href: '#va-solutions' },
        { name: 'VA Services', href: '#va-services' },
        { name: 'Our Programs', href: '#our-programs' }
      ]
    },
    { name: 'Pricing', href: '#pricing-packages' },
    { name: 'Contact', href: '#contact' }
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleDropdownToggle = (index) => {
    setActiveDropdown(activeDropdown === index ? null : index);
  };

  const handleLinkClick = (href) => {
    setActiveDropdown(null);
    setIsMobileMenuOpen(false);
    document.querySelector(href)?.scrollIntoView({
      behavior: 'smooth'
    });
  };

  return (
    <header className={`header ${isScrolled ? 'header--scrolled' : ''}`}>
      <div className="header__container">
        {/* Logo */}
        <div className="header__logo">
          <img
            src="/Iprovide-Solutions.jpg"
            alt="Iprovide Solutions"
            className="header__logo-image"
          />
        </div>

        {/* Desktop Navigation */}
        <nav className="header__nav">
          <ul className="header__nav-list">
            {navigationItems.map((item, index) => (
              <li key={index} className={`header__nav-item ${item.isDropdown ? 'header__nav-item--dropdown' : ''}`}>
                {item.isDropdown ? (
                  <>
                    <button
                      className={`header__nav-link header__nav-link--dropdown ${activeDropdown === index ? 'header__nav-link--active' : ''}`}
                      onClick={() => handleDropdownToggle(index)}
                    >
                      {item.name}
                      <svg className="header__dropdown-icon" width="12" height="12" viewBox="0 0 12 12" fill="none">
                        <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </button>
                    <div className={`header__dropdown ${activeDropdown === index ? 'header__dropdown--open' : ''}`}>
                      <ul className="header__dropdown-list">
                        {item.items.map((subItem, subIndex) => (
                          <li key={subIndex} className="header__dropdown-item">
                            <a
                              href={subItem.href}
                              className="header__dropdown-link"
                              onClick={(e) => {
                                e.preventDefault();
                                handleLinkClick(subItem.href);
                              }}
                            >
                              {subItem.name}
                            </a>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </>
                ) : (
                  <a
                    href={item.href}
                    className="header__nav-link"
                    onClick={(e) => {
                      e.preventDefault();
                      handleLinkClick(item.href);
                    }}
                  >
                    {item.name}
                  </a>
                )}
              </li>
            ))}
          </ul>
        </nav>

        {/* CTA Section */}
        <div className="header__cta">
          <button
            className="header__cta-button header__cta-button--secondary"
            onClick={() => {
              document.querySelector('#contact')?.scrollIntoView({
                behavior: 'smooth'
              });
            }}
          >
            Get Quote
          </button>
          <button
            className="header__cta-button header__cta-button--primary"
            onClick={() => {
              document.querySelector('#va-services')?.scrollIntoView({
                behavior: 'smooth'
              });
            }}
          >
            Start Now
          </button>
        </div>

        {/* Mobile Menu Button */}
        <button 
          className={`header__mobile-toggle ${isMobileMenuOpen ? 'header__mobile-toggle--active' : ''}`}
          onClick={toggleMobileMenu}
          aria-label="Toggle mobile menu"
        >
          <span className="header__mobile-toggle-line"></span>
          <span className="header__mobile-toggle-line"></span>
          <span className="header__mobile-toggle-line"></span>
        </button>
      </div>

      {/* Mobile Navigation */}
      <div className={`header__mobile-nav ${isMobileMenuOpen ? 'header__mobile-nav--open' : ''}`}>
        <ul className="header__mobile-nav-list">
          {navigationItems.map((item, index) => (
            <li key={index} className="header__mobile-nav-item">
              {item.isDropdown ? (
                <>
                  <button
                    className={`header__mobile-nav-link header__mobile-nav-link--dropdown ${activeDropdown === index ? 'header__mobile-nav-link--active' : ''}`}
                    onClick={() => handleDropdownToggle(index)}
                  >
                    {item.name}
                    <svg className="header__mobile-dropdown-icon" width="12" height="12" viewBox="0 0 12 12" fill="none">
                      <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                  <div className={`header__mobile-dropdown ${activeDropdown === index ? 'header__mobile-dropdown--open' : ''}`}>
                    {item.items.map((subItem, subIndex) => (
                      <a
                        key={subIndex}
                        href={subItem.href}
                        className="header__mobile-dropdown-link"
                        onClick={(e) => {
                          e.preventDefault();
                          handleLinkClick(subItem.href);
                        }}
                      >
                        {subItem.name}
                      </a>
                    ))}
                  </div>
                </>
              ) : (
                <a
                  href={item.href}
                  className="header__mobile-nav-link"
                  onClick={(e) => {
                    e.preventDefault();
                    handleLinkClick(item.href);
                  }}
                >
                  {item.name}
                </a>
              )}
            </li>
          ))}
        </ul>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="header__overlay"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </header>
  );
};

export default Header;
