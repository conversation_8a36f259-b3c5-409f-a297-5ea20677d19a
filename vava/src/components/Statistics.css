/* Statistics Section */
.statistics {
  padding: 120px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
}

.statistics__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 32px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.statistics__content {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

/* Header Section */
.statistics__header {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.statistics__badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.1), rgba(3, 124, 193, 0.1));
  border: 1px solid rgba(249, 91, 0, 0.2);
  border-radius: 24px;
  backdrop-filter: blur(8px);
  width: fit-content;
}

.statistics__badge-text {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-orange);
  letter-spacing: -0.01em;
}

.statistics__title {
  font-size: 40px;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  margin: 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.statistics__description {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.statistics__description p {
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin: 0;
}

/* Stats Grid */
.statistics__stats {
  margin-top: 24px;
}

.statistics__stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

.statistics__stat-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 32px 24px;
  backdrop-filter: blur(16px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.statistics__stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.02), rgba(3, 124, 193, 0.02));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.statistics__stat-card:hover::before {
  opacity: 1;
}

.statistics__stat-number {
  font-size: 48px;
  font-weight: 700;
  color: var(--primary-orange);
  line-height: 1;
  margin-bottom: 8px;
  font-family: 'DM Sans', sans-serif;
}

.statistics__stat-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 16px;
}

.statistics__stat-bar {
  width: 100%;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.statistics__stat-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-orange), var(--primary-blue));
  border-radius: 2px;
  transition: width 2s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Visual Section */
.statistics__visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.statistics__map-container {
  position: relative;
  width: 400px;
  height: 300px;
  background: linear-gradient(135deg, rgba(249, 91, 0, 0.05), rgba(3, 124, 193, 0.05));
  border-radius: 24px;
  padding: 40px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.statistics__map-bg {
  width: 100%;
  height: 100%;
  position: relative;
}

.statistics__map {
  width: 100%;
  height: 100%;
}

.statistics__map-path {
  fill: linear-gradient(135deg, var(--primary-orange), var(--primary-blue));
  stroke: var(--primary-blue);
  stroke-width: 2;
  opacity: 0.8;
}

.statistics__map-pin {
  fill: var(--primary-orange);
  animation: pulse 2s infinite;
}

.statistics__floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.statistics__floating-item {
  position: absolute;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: float 6s ease-in-out infinite;
}

.statistics__floating-item--1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.statistics__floating-item--2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.statistics__floating-item--3 {
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .statistics__container {
    gap: 60px;
    padding: 0 24px;
  }

  .statistics__title {
    font-size: 36px;
  }

  .statistics__stats-grid {
    gap: 24px;
  }

  .statistics__stat-card {
    padding: 24px 16px;
  }

  .statistics__stat-number {
    font-size: 40px;
  }

  .statistics__map-container {
    width: 350px;
    height: 260px;
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .statistics {
    padding: 80px 0;
  }

  .statistics__container {
    grid-template-columns: 1fr;
    gap: 48px;
    text-align: center;
  }

  .statistics__content {
    gap: 32px;
  }

  .statistics__title {
    font-size: 32px;
  }

  .statistics__stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 400px;
    margin: 0 auto;
  }

  .statistics__map-container {
    width: 300px;
    height: 220px;
    margin: 0 auto;
  }

  .statistics__floating-item {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .statistics__container {
    padding: 0 16px;
    gap: 32px;
  }

  .statistics__title {
    font-size: 28px;
  }

  .statistics__description p {
    font-size: 14px;
  }

  .statistics__stat-card {
    padding: 20px 16px;
  }

  .statistics__stat-number {
    font-size: 32px;
  }

  .statistics__stat-label {
    font-size: 12px;
  }

  .statistics__map-container {
    width: 250px;
    height: 180px;
    padding: 20px;
  }

  .statistics__floating-item {
    width: 32px;
    height: 32px;
  }

  .statistics__floating-item svg {
    width: 16px;
    height: 16px;
  }
}
